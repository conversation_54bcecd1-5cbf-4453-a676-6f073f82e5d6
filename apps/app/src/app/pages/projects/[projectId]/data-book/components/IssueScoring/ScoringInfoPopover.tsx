import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { Tooltip, TooltipContent, TooltipTrigger } from '@shape-construction/arch-ui/src/Tooltip/Tooltip';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { ScoringInfo } from './ScoringInfo';

export const ScoringInfoPopover: React.FC = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));

  return (
    <Popover>
      <Popover.Trigger>
        <Tooltip delayDuration={100}>
          <TooltipTrigger className="flex items-center">
            {isLargeScreen ? (
              <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                {messages('performanceDetails.issueReportsTable.scoringInfo.scoringInfoCTA')}
              </Button>
            ) : (
              <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon} />
            )}
          </TooltipTrigger>
          <TooltipContent align="start">
            {messages('performanceDetails.issueReportsTable.scoringInfo.tooltipText')}
          </TooltipContent>
        </Tooltip>
      </Popover.Trigger>
      <Popover.Content align="start" className="p-0 max-h-[320px]">
        <ScoringInfo />
      </Popover.Content>
    </Popover>
  );
};
